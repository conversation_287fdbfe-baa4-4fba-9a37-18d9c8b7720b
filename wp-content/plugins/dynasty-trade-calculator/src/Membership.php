<?php
/**
 * Dynasty Trade Calculator - Membership Class
 *
 * Handles all membership-related functionality including:
 * - Customer and membership retrieval
 * - RotoGPT integration
 * - Membership status transitions
 * - WordPress hooks and actions
 */

namespace DynastyTradeCalculator;

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Membership
{
    /**
     * Singleton instance
     */
    private static $instance = null;

    /**
     * Empty constructor - no initialization needed
     */
    public function __construct()
    {
        // Empty constructor as requested
    }

    /**
     * Get singleton instance
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize membership functionality and hooks
     * Call this method to set up all WordPress hooks and shortcodes
     */
    public function init()
    {
        // Register WordPress hooks
        add_action('rcp_transition_membership_status', [$this, 'handleMembershipStatusTransition'], 10, 3);
        // Run at priority 5 to execute BEFORE RCP's automatic activation (which runs at priority 10)
        add_action('rcp_new_membership_added', [$this, 'handleNewMembershipAdded'], 5, 1);
        // Hook into payment completion to prevent automatic activation of scheduled downgrades
        add_action('rcp_update_payment_status_complete', [$this, 'preventDowngradeActivation'], 5, 1);

        // Disable proration for downgrades only
        add_filter('rcp_membership_get_prorate_credit', [$this, 'disableProrationForDowngrades'], 10, 3);
        // Also hook into the membership level expiration calculation to prevent proration there
        add_filter('rcp_membership_level_get_expiration_length', [$this, 'adjustExpirationLengthForDowngrades'], 10, 3);

        // Register shortcodes
        add_shortcode('dtc_register_form', [$this, 'registerFormShortcode']);
        add_shortcode('dtc-easy-pricing-table', [$this, 'easyPricingTableShortcode']);
    }
    /**
     * Get the current user's RCP customer object
     *
     * @return \RCP_Customer|null Customer object or null if not found
     */
    public function getCurrentUserCustomer()
    {
        $current_user = wp_get_current_user();
        $current_user_id = $current_user->ID;
        $current_customer = rcp_get_customer_by_user_id($current_user_id);

        return $current_customer;
    }

    /**
     * Get the current user's active or cancelled membership
     *
     * @return \RCP_Membership|null Membership object or null if not found/expired
     */
    public function getCurrentUserMembership()
    {
        $current_customer = $this->getCurrentUserCustomer();
        $current_membership = $current_customer ? ($current_customer->get_memberships(['status' => ['active', 'cancelled']])[0] ?? null) : null;

        // If membership is cancelled, check if it's still valid
        if ($current_membership && $current_membership->get_status() === 'cancelled') {
            $expiration_date = strtotime($current_membership->get_expiration_date(false));
            if ($expiration_date < time()) {
                return null;
            }
        }

        return $current_membership;
    }

    /**
     * Get the current user's membership that was active at a specific date
     *
     * @param string $date Date to check membership for
     * @return \RCP_Membership|null Membership object or null if not found
     */
    public function getCurrentUserMembershipAtDate($date)
    {
        $current_customer = $this->getCurrentUserCustomer();
        if (empty($current_customer)) {
            return [];
        }

        $memberships = $current_customer->get_memberships();
        if (empty($memberships)) {
            return [];
        }
        foreach ($memberships as $membership) {
            if (
                $membership->get_activated_date()
                && $membership->get_expiration_date(false)
                && $membership->get_activated_date() <= $date
                && $membership->get_expiration_date(false) >= $date
            ) {
                return $membership;
            }
        }

        return null;
    }

    /**
     * Check if a customer is invited to the ChatDTC pilot program
     *
     * @param \RCP_Customer $customer Customer object to check
     * @return bool True if invited, false otherwise
     */
    public function isCustomerInvitedToChatdtcPilot($customer)
    {
        $notes = $customer->get_notes();
        $is_invited = strpos($notes, DTC_CHATDTC_PILOT_INVITATION_TEXT) !== false;
        if (!$is_invited) {
            $membership = $customer->get_memberships(['status' => 'active'])[0] ?? null;
            if (
                $membership
                && (
                    $membership->get_recurring_amount() == 2.99
                    || $membership->get_recurring_amount() == 29.99
                )
            ) {
                $is_invited = true;
            }
        }

        return $is_invited;
    }

    /**
     * Check if a customer has lost legacy membership options
     *
     * @param \RCP_Customer $customer Customer object to check
     * @return bool True if legacy options lost, false otherwise
     */
    public function isLegacyMembershipOptionsLost($customer)
    {
        $notes = $customer->get_notes();
        $is_legacy_lost = strpos($notes, DTC_LEGACY_MEMBERSHIP_LOST_NOTE) !== false;

        return $is_legacy_lost;
    }

    /**
     * Check if the current user is invited to the ChatDTC pilot program
     *
     * @return bool True if invited, false otherwise
     */
    public function isCurrentUserInvitedToChatdtcPilot()
    {
        $current_customer = $this->getCurrentUserCustomer();
        if (empty($current_customer)) {
            return false;
        }

        return $this->isCustomerInvitedToChatdtcPilot($current_customer);
    }

    /**
     * Get the RotoGPT subscription type for a membership
     *
     * @param \RCP_Membership $membership Membership object
     * @return string|null RotoGPT subscription type or null if not found
     */
    public function getRotoGptSubscription($membership)
    {
        $rotogpt_subscription = null;
        $is_customer_invited_to_pilot = $this->isCustomerInvitedToChatdtcPilot($membership->get_customer());
        $membership_level = $membership->get_object_id();

        Debug::debug('Membership level in getRotoGptSubscription: ' . $membership_level);
        Debug::logObject(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE');

        foreach (DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE as $mapping) {
            if ($mapping['membership_level_id'] == $membership_level) {
                Debug::logObject($mapping, 'Found mapping for membership level ' . $membership_level);
                $rotogpt_subscription = $mapping['rotogpt_subscription_type'];
                break;
            }
        }

        if (empty($rotogpt_subscription) && $is_customer_invited_to_pilot && DTC_IS_CHATDTC_PILOT_ACTIVE) {
            $rotogpt_subscription = DTC_ROTOGPT_TEST_DRIVE_SUBSCRIPTION;
        }

        if (empty($rotogpt_subscription) && !DTC_IS_CHATDTC_PILOT_ACTIVE) {
            $rotogpt_subscription = DTC_ROTOGPT_TEST_DRIVE_SUBSCRIPTION;
        }

        return $rotogpt_subscription;
    }

    /**
     * Sign in to RotoGPT and get access token
     *
     * @param \RCP_Membership $membership Membership object
     * @param string $rotogpt_subscription RotoGPT subscription type
     * @return string|false Access token or false on failure
     */
    public function rotoGptSignin($membership, $rotogpt_subscription)
    {
        $rotogpt_signin_request_body = json_encode([
            'client_id' => DTC_ROTOGPT_CLIENT_ID,
            'client_password' => DTC_ROTOGPT_PASSWORD,
            'current_user' => [
                'user_id' => (string) $membership->get_customer()->get_id(),
                'membership' => $rotogpt_subscription,
                'sign_up_date' => date('d-m-Y H:i:s', strtotime($membership->get_activated_date())),
            ],
        ]);

        $api_endpoint = DTC_IS_PRODUCTION ? 'https://api.rotogpt.com/signin' : 'https://api.dev.rotogpt.com/signin';

        Debug::logApiCall($api_endpoint, json_decode($rotogpt_signin_request_body, true), null, 'Signin');

        $rotogpt_signin_response = wp_remote_post(
            $api_endpoint,
            [
                'body' => $rotogpt_signin_request_body,
                'timeout' => 30,
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
            ]
        );

        Debug::logObject($rotogpt_signin_response, 'DTC RotoGPT Signin: Response');

        // Check for HTTP errors
        if (is_wp_error($rotogpt_signin_response)) {
            Debug::error('DTC RotoGPT Signin Error: HTTP request failed - ' . $rotogpt_signin_response->get_error_message(), true);
            return false;
        }

        // Check response code
        $response_code = wp_remote_retrieve_response_code($rotogpt_signin_response);
        if ($response_code !== 200) {
            Debug::error('DTC RotoGPT Signin Error: HTTP ' . $response_code, true);
            Debug::error('DTC RotoGPT Signin Response: ' . wp_remote_retrieve_body($rotogpt_signin_response), true);
            return false;
        }

        $rotogpt_signin_response_body = wp_remote_retrieve_body($rotogpt_signin_response);
        $rotogpt_signin_response = json_decode($rotogpt_signin_response_body, true);

        Debug::logObject($rotogpt_signin_response, 'DTC RotoGPT Signin: Response body');

        return $rotogpt_signin_response['accessToken'];
    }

    /**
     * Update an existing RotoGPT subscription for upgrades/downgrades
     *
     * @param \RCP_Membership $membership New membership object
     * @param \RCP_Membership $old_membership Old membership object
     * @param string $rotogpt_subscription_type New RotoGPT subscription type
     * @param string $old_rotogpt_subscription Old RotoGPT subscription type
     * @param string $new_rotogpt_subscription New RotoGPT subscription type
     * @return bool Success status
     */
    public function rotoGptUpdateSubscription($membership, $old_membership, $rotogpt_subscription_type, $old_rotogpt_subscription, $new_rotogpt_subscription) {
        $membership_id = $membership->get_id();

        // Determine if this is an upgrade or downgrade and when to apply the change
        // instead of pricing we need to follow the DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE order
        // e.x. low to high: free, standard_50, standard_100, standard_200, vip, admin
        $is_upgrade = false;
        $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
        $old_rotogpt_subscription_index = array_search($old_rotogpt_subscription, $rotogpt_subscriptions);
        $new_rotogpt_subscription_index = array_search($new_rotogpt_subscription, $rotogpt_subscriptions);
        if ($new_rotogpt_subscription_index > $old_rotogpt_subscription_index) {
            $is_upgrade = true;
        }

        $user_id = $membership->get_customer()->get_id();
        $request = [
            'user_id' => (string) $user_id,
            'client_id' => DTC_ROTOGPT_CLIENT_ID,
            'subscription_type' => $rotogpt_subscription_type,
        ];

        if ($is_upgrade) {
            // For upgrades, apply immediately
            $request['apply_immediately'] = true;
        } else {
            // For downgrades, apply at the end of the current subscription period
            $request['apply_immediately'] = false;

            // Get the expiration date of the old membership
            $old_expiration = $old_membership->get_expiration_date();
            if (!empty($old_expiration) && $old_expiration !== '0000-00-00 00:00:00') {
                // Format the date for RotoGPT API (assuming they expect ISO format)
                $start_date = date('Y-m-d\TH:i:s\Z', strtotime($old_expiration));
                $request['new_subscription_start_date'] = $start_date;
            } else {
                // If no expiration date, apply immediately as fallback
                $request['apply_immediately'] = true;
                unset($request['new_subscription_start_date']);
            }
        }

        // Use the new membership for signin (it has the current user info)
        $access_token = $this->rotoGptSignin($membership, $new_rotogpt_subscription ?: $old_rotogpt_subscription);

        if (!$access_token) {
            Debug::error('DTC RotoGPT Update Error: Failed to get access token for membership #' . $membership_id, true);
            return false;
        }

        $api_endpoint = DTC_IS_PRODUCTION
            ? 'https://api.rotogpt.com/subscriptions/update'
            : 'https://api.dev.rotogpt.com/subscriptions/update';

        Debug::logApiCall($api_endpoint, $request, null, 'Update');

        $response = wp_remote_post(
            $api_endpoint,
            [
                'body' => json_encode($request),
                'timeout' => 30,
                'headers' => [
                    'Authorization' => "Bearer {$access_token}",
                    'Content-Type' => 'application/json',
                ],
            ]
        );

        // Check for HTTP errors
        if (is_wp_error($response)) {
            Debug::error('DTC RotoGPT Update Error: HTTP request failed - ' . $response->get_error_message(), true);
            return false;
        }

        // Check response code
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            Debug::error('DTC RotoGPT Update Error: HTTP ' . $response_code, true);
            Debug::error('DTC RotoGPT Update Response: ' . wp_remote_retrieve_body($response), true);
            return false;
        }

        Debug::info('DTC RotoGPT Update: Response code: ' . $response_code);
        Debug::logObject(wp_remote_retrieve_body($response), 'DTC RotoGPT Update: Response body');

        // Add note to customer about the RotoGPT subscription update
        $old_level_name = $old_membership->get_membership_level_name();
        $new_level_name = $membership->get_membership_level_name();

        if ($is_upgrade) {
            $note = sprintf(
                'RotoGPT subscription upgraded from %s (%s) to %s (%s) - applied immediately',
                $old_level_name,
                $old_rotogpt_subscription ?: 'none',
                $new_level_name,
                $rotogpt_subscription_type
            );
        } else {
            $schedule_info = isset($request['new_subscription_start_date'])
                ? ' - scheduled for ' . $request['new_subscription_start_date']
                : ' - applied immediately (no expiration date found)';
            $note = sprintf(
                'RotoGPT subscription downgraded from %s (%s) to %s (%s)%s',
                $old_level_name,
                $old_rotogpt_subscription ?: 'none',
                $new_level_name,
                $rotogpt_subscription_type,
                $schedule_info
            );
        }

        $membership->get_customer()->add_note($note);

        $log_message = $is_upgrade
            ? 'DTC RotoGPT Upgrade Success: Updated user #' . $user_id . ' from ' . ($old_rotogpt_subscription ?: 'none') . ' to ' . $rotogpt_subscription_type . ' (immediate)'
            : 'DTC RotoGPT Downgrade Success: Scheduled user #' . $user_id . ' from ' . ($old_rotogpt_subscription ?: 'none') . ' to ' . $rotogpt_subscription_type . (isset($request['new_subscription_start_date']) ? ' starting ' . $request['new_subscription_start_date'] : ' (immediate - no expiration)');

        Debug::info($log_message);
        return true;
    }

    /**
     * Create a new RotoGPT subscription for a new user
     *
     * @param \RCP_Membership $membership Membership object
     * @param string $rotogpt_subscription_type RotoGPT subscription type
     * @return bool Success status
     */
    public function rotoGptCreateSubscription($membership, $rotogpt_subscription_type) {
        $user_id = $membership->get_customer()->get_id();
        Debug::info('DTC RotoGPT Create: Creating subscription for user # (customer id)' . $user_id . ' with type ' . $rotogpt_subscription_type);

        $request = [
            'user_id' => (string) $user_id,
            'client_id' => DTC_ROTOGPT_CLIENT_ID,
            'subscription_type' => $rotogpt_subscription_type,
        ];

        $api_endpoint = DTC_IS_PRODUCTION
            ? 'https://api.rotogpt.com/subscriptions/create'
            : 'https://api.dev.rotogpt.com/subscriptions/create';

        Debug::logApiCall($api_endpoint, $request, null, 'Create');

        $response = wp_remote_post(
            $api_endpoint,
            [
                'body' => json_encode($request),
                'timeout' => 30,
                'headers' => [
                    // 'Authorization' => "Bearer {$access_token}", // not needed
                    'Content-Type' => 'application/json',
                ],
            ]
        );

        Debug::logObject($response, 'DTC RotoGPT Create: Response');

        // Check for HTTP errors
        if (is_wp_error($response)) {
            Debug::error('DTC RotoGPT Create Error: HTTP request failed - ' . $response->get_error_message(), true);
            return false;
        }

        // Check response code
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            Debug::error('DTC RotoGPT Create Error: HTTP ' . $response_code, true);
            Debug::error('DTC RotoGPT Create Response: ' . wp_remote_retrieve_body($response), true);
            return false;
        }

        Debug::info('DTC RotoGPT Create: Response code: ' . $response_code);
        Debug::logObject(wp_remote_retrieve_body($response), 'DTC RotoGPT Create: Response body');

        // Add note to customer about the RotoGPT subscription creation
        $level_name = $membership->get_membership_level_name();
        $note = sprintf(
            'RotoGPT subscription created: %s (%s)',
            $level_name,
            $rotogpt_subscription_type
        );
        $membership->get_customer()->add_note($note);

        Debug::info('DTC RotoGPT Create Success: Created subscription for user #' . $user_id . ' with type ' . $rotogpt_subscription_type);
        return true;
    }

    /**
     * Handle membership status transitions - specifically when memberships expire
     * This function cancels the RotoGPT account when a membership expires
     * ENHANCED: Also handles scheduled downgrade activation
     */
    public function handleMembershipStatusTransition($old_status, $new_status, $membership_id)
    {
        Debug::info('DTC Membership Status Transition: Membership #' . $membership_id . ' transitioned from ' . $old_status . ' to ' . $new_status);
        $membership = rcp_get_membership($membership_id);
        $customer = $membership->get_customer();
        $customer->add_note(DTC_LEGACY_MEMBERSHIP_LOST_NOTE);

        $rogogpt_subscription = $this->getRotoGptSubscription($membership);
        if (empty($rogogpt_subscription)) {
            return;
        }
        
        if ($new_status == 'expired') {
            $pending_memberships = $customer->get_memberships(['status' => 'pending']);
            $pending_downgrade_id = is_array($pending_memberships) && count($pending_memberships) ? $pending_memberships[0]->get_id() : null;
            
            if (!empty($pending_downgrade_id)) {
                Debug::info('DTC Downgrade Activation: Expired membership #' . $membership_id . ' has pending downgrade to #' . $pending_downgrade_id);

                $pending_membership = rcp_get_membership($pending_downgrade_id);
                if ($pending_membership && $pending_membership->get_status() === 'pending') {
                    $pending_membership->set_status('active');

                    $pending_membership->add_note('Scheduled downgrade activated - membership is now active');
                    $membership->add_note('Expired - scheduled downgrade to membership #' . $pending_downgrade_id . ' has been activated');

                    Debug::info('DTC Downgrade Activation: Successfully activated pending membership #' . $pending_downgrade_id);
                }

                return;
            }
        } else {
            // Handle other status transitions if needed
            return;
        }

        $access_token = $this->rotoGptSignin($membership, $rogogpt_subscription);

        $user_id = $membership->get_customer()->get_id();
        $request = [
            'user_id' => (string) $user_id,
            'client_id' => DTC_ROTOGPT_CLIENT_ID,
            'subscription_type' => 'cancelled_account',
            'apply_immediately' => true,
        ];
        $response = wp_remote_post(
            DTC_IS_PRODUCTION
                ? 'https://api.rotogpt.com//subscriptions/update'
                : 'https://api.dev.rotogpt.com/subscriptions/update',
                // : 'https://api.dev.rotogpt.com/cancel_account', // old enddpoint
            [
                'body' => json_encode($request),
                'timeout' => 30,
                'headers' => [
                    'Authorization' => "Bearer {$access_token}",
                    'Content-Type' => 'application/json',
                ],
            ]
        );
        $response_body = wp_remote_retrieve_body($response);
        $response = json_decode($response_body, true);
    }

    /**
     * Prevent automatic activation of scheduled downgrades when payment is completed
     * This runs BEFORE rcp_complete_registration to prevent unwanted activation
     */
    public function preventDowngradeActivation($payment_id)
    {
        global $rcp_payments_db;

        $payment = $rcp_payments_db->get_payment($payment_id);
        if (empty($payment) || empty($payment->membership_id)) {
            return;
        }

        $membership = rcp_get_membership($payment->membership_id);
        if (empty($membership)) {
            return;
        }

        // Check if this is a downgrade that should be scheduled
        if ($membership->was_upgrade()) {
            $old_membership_id = $membership->get_upgraded_from();
            $old_membership = rcp_get_membership($old_membership_id);

            if (!empty($old_membership)) {
                $old_rotogpt_subscription = $this->getRotoGptSubscription($old_membership);
                $new_rotogpt_subscription = $this->getRotoGptSubscription($membership);

                // Determine if this is an upgrade or downgrade: Custom LOGIC - if subscription_type similar, then we skip this.
                if ($old_rotogpt_subscription === $new_rotogpt_subscription) {
                    return;
                }
                
                // Determine if this is a downgrade
                $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
                $old_rotogpt_subscription_index = array_search($old_rotogpt_subscription, $rotogpt_subscriptions);
                $new_rotogpt_subscription_index = array_search($new_rotogpt_subscription, $rotogpt_subscriptions);
                $is_upgrade = ($new_rotogpt_subscription_index > $old_rotogpt_subscription_index);

                if (!$is_upgrade) {
                    $old_expiration = $old_membership->get_expiration_date();

                    if (!empty($old_expiration) && $old_expiration !== '0000-00-00 00:00:00') {
                        Debug::info('DTC Downgrade Fix: Preventing automatic activation of scheduled downgrade membership #' . $membership->get_id());

                        // Temporarily remove the rcp_complete_registration hook to prevent activation
                        remove_action('rcp_update_payment_status_complete', 'rcp_complete_registration', 10);

                        // Add our custom completion handler that skips activation
                        add_action('rcp_update_payment_status_complete', [$this, 'completeDowngradeRegistrationWithoutActivation'], 10, 1);
                    }
                }
            }
        }
    }

    /**
     * Custom registration completion for downgrades that skips activation
     */
    public function completeDowngradeRegistrationWithoutActivation($payment_id)
    {
        // Re-add the original hook for future payments
        add_action('rcp_update_payment_status_complete', 'rcp_complete_registration', 10);

        // Remove our custom hook
        remove_action('rcp_update_payment_status_complete', [$this, 'completeDowngradeRegistrationWithoutActivation'], 10);

        Debug::info('DTC Downgrade Fix: Completed downgrade registration without activation for payment #' . $payment_id);
    }

    /**
     * Disable proration for downgrades only
     * This prevents RCP from extending the expiration date of downgraded memberships
     *
     * @param float $discount The proration credit amount
     * @param int $membership_id The membership ID
     * @param \RCP_Membership $membership The membership object
     * @return float Modified discount amount (0 for downgrades, original for upgrades)
     */
    public function disableProrationForDowngrades($discount, $membership_id, $membership)
    {
        // Only process if this is an upgrade/downgrade scenario
        if (!$membership->was_upgrade()) {
            return $discount;
        }

        $old_membership_id = $membership->get_upgraded_from();
        $old_membership = rcp_get_membership($old_membership_id);

        if (empty($old_membership)) {
            return $discount;
        }

        // Get RotoGPT subscription types to determine if this is a downgrade
        $old_rotogpt_subscription = $this->getRotoGptSubscription($old_membership);
        $new_rotogpt_subscription = $this->getRotoGptSubscription($membership);

        // If same subscription type, allow normal proration
        if ($old_rotogpt_subscription === $new_rotogpt_subscription) {
            return $discount;
        }

        // Determine if this is a downgrade based on subscription type order
        $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
        $old_rotogpt_subscription_index = array_search($old_rotogpt_subscription, $rotogpt_subscriptions);
        $new_rotogpt_subscription_index = array_search($new_rotogpt_subscription, $rotogpt_subscriptions);
        $is_upgrade = ($new_rotogpt_subscription_index > $old_rotogpt_subscription_index);

        if (!$is_upgrade) {
            // This is a downgrade - disable proration
            Debug::info('DTC Proration: Disabling proration for downgrade from ' . $old_rotogpt_subscription . ' to ' . $new_rotogpt_subscription . ' (membership #' . $membership_id . ')');
            return 0;
        }

        // This is an upgrade - allow normal proration
        Debug::info('DTC Proration: Allowing proration for upgrade from ' . $old_rotogpt_subscription . ' to ' . $new_rotogpt_subscription . ' (membership #' . $membership_id . ')');
        return $discount;
    }

    /**
     * Adjust expiration length for downgrades to prevent proration extension
     * This ensures downgraded memberships get the standard duration, not extended by proration
     *
     * @param int $expiration_length The calculated expiration length
     * @param int $membership_level_id The membership level ID
     * @param int $upgraded_from The ID of the membership being upgraded from
     * @return int Modified expiration length
     */
    public function adjustExpirationLengthForDowngrades($expiration_length, $membership_level_id, $upgraded_from)
    {
        // Only process if this is an upgrade/downgrade scenario
        if (empty($upgraded_from)) {
            return $expiration_length;
        }

        $old_membership = rcp_get_membership($upgraded_from);
        if (empty($old_membership)) {
            return $expiration_length;
        }

        // Create a temporary membership object to check subscription types
        $membership_level = rcp_get_membership_level($membership_level_id);
        if (empty($membership_level)) {
            return $expiration_length;
        }

        // Get RotoGPT subscription types
        $old_rotogpt_subscription = $this->getRotoGptSubscription($old_membership);

        // For the new subscription, we need to look it up by membership level
        $new_rotogpt_subscription = null;
        foreach (DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE as $mapping) {
            if ($mapping['membership_level_id'] == $membership_level_id) {
                $new_rotogpt_subscription = $mapping['rotogpt_subscription_type'];
                break;
            }
        }

        if (empty($new_rotogpt_subscription)) {
            return $expiration_length;
        }

        // If same subscription type, allow normal proration
        if ($old_rotogpt_subscription === $new_rotogpt_subscription) {
            return $expiration_length;
        }

        // Determine if this is a downgrade based on subscription type order
        $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
        $old_rotogpt_subscription_index = array_search($old_rotogpt_subscription, $rotogpt_subscriptions);
        $new_rotogpt_subscription_index = array_search($new_rotogpt_subscription, $rotogpt_subscriptions);
        $is_upgrade = ($new_rotogpt_subscription_index > $old_rotogpt_subscription_index);

        if (!$is_upgrade) {
            // This is a downgrade - return the standard membership level duration without proration
            $standard_length = $membership_level->get_duration();
            Debug::info('DTC Proration: Adjusting expiration length for downgrade from ' . $old_rotogpt_subscription . ' to ' . $new_rotogpt_subscription . ' - using standard length: ' . $standard_length . ' instead of prorated: ' . $expiration_length);
            return $standard_length;
        }

        // This is an upgrade - allow normal proration
        Debug::info('DTC Proration: Allowing normal expiration length for upgrade from ' . $old_rotogpt_subscription . ' to ' . $new_rotogpt_subscription);
        return $expiration_length;
    }

    /**
     * Handle new memberships and membership upgrades/downgrades
     * This function creates or updates RotoGPT subscriptions when membership changes occur
     */
    public function handleNewMembershipAdded($membership_id)
    {
        Debug::info('DTC RotoGPT: New membership added - membership ID: ' . $membership_id);
        
        $membership = rcp_get_membership($membership_id);
        $customer = $membership->get_customer();

        Debug::logMembership('Processing', $membership_id, [
            'membership_level' => $membership->get_object_id(),
            'customer_id' => $customer->get_id(),
            'membership_status' => $membership->get_status(),
            'is_upgrade' => $membership->was_upgrade(),
        ]);

        // Check if this is an upgrade/downgrade or a brand new membership
        $is_upgrade_downgrade = $membership->was_upgrade();

        // If same subscription_type (free) and RCP says upgrade (based on price), then we follow RCP.
        if ($is_upgrade_downgrade) {
            Debug::info('DTC RotoGPT: Membership #' . $membership_id . ' is an upgrade/downgrade - using update endpoint');

            // Get the old membership that was upgraded/downgraded from
            $old_membership_id = $membership->get_upgraded_from();
            $old_membership = rcp_get_membership($old_membership_id);

            if (empty($old_membership)) {
                Debug::error('DTC RotoGPT Update Error: Could not find old membership #' . $old_membership_id, true);
                return;
            }

            $old_rotogpt_subscription = $this->getRotoGptSubscription($old_membership);
            $new_rotogpt_subscription = $this->getRotoGptSubscription($membership);

            // Determine if this is an upgrade or downgrade: Custom LOGIC - if subscription_type similar, then we skip this.
            if ($old_rotogpt_subscription === $new_rotogpt_subscription) {
                Debug::info('DTC RotoGPT: Membership #' . $membership_id . ' is same subscription type - skipping');
                return;
            }

            $rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
            $old_rotogpt_subscription_index = array_search($old_rotogpt_subscription, $rotogpt_subscriptions);
            $new_rotogpt_subscription_index = array_search($new_rotogpt_subscription, $rotogpt_subscriptions);
            $is_upgrade = ($new_rotogpt_subscription_index > $old_rotogpt_subscription_index);

            if (!$is_upgrade) {
                Debug::info('DTC Downgrade Fix: Detected downgrade - preserving current membership until expiration');

                $old_expiration = $old_membership->get_expiration_date();

                if (!empty($old_expiration) && $old_expiration !== '0000-00-00 00:00:00') {
                    Debug::info('DTC Downgrade Fix: Preserving expiration date: ' . $old_expiration);

                    $old_membership->set_status('active');
                    $membership->update([
                        'status' => 'pending',
                        'created_date' => $old_expiration, // Will activate when old expires
                        'activated_date' => $old_expiration
                    ]);

                    $old_membership->add_note(sprintf(
                        'Downgrade scheduled: Will change to %s (Level %d) on %s',
                        $membership->get_membership_level_name(),
                        $membership->get_object_id(),
                        date('Y-m-d', strtotime($old_expiration))
                    ));

                    $membership->add_note(sprintf(
                        'Scheduled downgrade from %s (Level %d) - will activate on %s',
                        $old_membership->get_membership_level_name(),
                        $old_membership->get_object_id(),
                        date('Y-m-d', strtotime($old_expiration))
                    ));

                    Debug::info('DTC Downgrade Fix: Successfully scheduled downgrade for ' . $old_expiration);

                    // For scheduled downgrades, we're done - the WordPress membership scheduling is complete
                    // return;
                } else {
                    Debug::info('DTC Downgrade Fix: No expiration date found, proceeding with immediate downgrade');
                }
            }
            
            // Only proceed if the new membership has a valid RotoGPT subscription
            if (empty($new_rotogpt_subscription)) {
                Debug::error('DTC RotoGPT Update Error: No RotoGPT subscription type found for new membership level', true);
                return;
            }

            // Call the update function
            $success = $this->rotoGptUpdateSubscription(
                $membership,
                $old_membership,
                $new_rotogpt_subscription,
                $old_rotogpt_subscription,
                $new_rotogpt_subscription
            );

            if (!$success) {
                Debug::error('DTC RotoGPT Update Error: Failed to update RotoGPT subscription for membership #' . $membership_id, true);
            }

        } else {
            Debug::info('DTC RotoGPT: Membership #' . $membership_id . ' is a new membership - using create endpoint');

            // This is a brand new membership, create a new RotoGPT subscription
            $rotogpt_subscription_type = $this->getRotoGptSubscription($membership);

            if (empty($rotogpt_subscription_type)) {
                Debug::error('DTC RotoGPT Create Error: No RotoGPT subscription type found for membership level', true);
                return;
            }

            // Call the create function
            $success = $this->rotoGptCreateSubscription($membership, $rotogpt_subscription_type);

            if (!$success) {
                Debug::error('DTC RotoGPT Create Error: Failed to create RotoGPT subscription for membership #' . $membership_id, true);
            }
        }
    }

    /**
     * Registration form shortcode
     * Displays available membership levels based on user's current status and eligibility
     */
    public function registerFormShortcode($atts = [])
    {
        $membership = $this->getCurrentUserMembershipAtDate(DTC_LEGACY_MEMBERSHIP_REFERENCE_DATE);
        $is_legacy_lost = $membership ? $this->isLegacyMembershipOptionsLost($membership->get_customer()) : true;
        $current_membership = $this->getCurrentUserMembership();
        $membership_levels = [];

        foreach (DTC_MEMBERSHIP_LEVEL_RULES as $rule) {
            $membership_level = $GLOBALS['rcp_levels_db']->get_level($rule['membership_level_id']);
            if ($current_membership && $current_membership->get_recurring_amount() == $membership_level->price) {
                continue;
            }
            if (
                $rule['valid_for_users_that_paid_any_of_these_amounts_only']
                && (
                    empty($membership)
                    || $is_legacy_lost
                    || !in_array($membership->get_recurring_amount(), $rule['valid_for_users_that_paid_any_of_these_amounts_only'])
                )
            ) {
                continue;
            }
            if (
                $rule['is_chatdtc_membership']
                && DTC_IS_CHATDTC_PILOT_ACTIVE
                && !$this->isCurrentUserInvitedToChatdtcPilot()
            ) {
                continue;
            }
            $membership_levels[] = $rule['membership_level_id'];
        }

        return do_shortcode('[register_form ids="' . implode(',', $membership_levels) . '"]');
    }

    /**
     * Easy pricing table shortcode
     * Displays the appropriate pricing table based on user's status and pilot eligibility
     */
    public function easyPricingTableShortcode($atts = [])
    {
        Debug::logObject($atts, 'Easy Pricing Table Shortcode Attributes');
        $membership = $this->getCurrentUserMembershipAtDate(DTC_LEGACY_MEMBERSHIP_REFERENCE_DATE);
        $is_legacy_lost = $membership ? $this->isLegacyMembershipOptionsLost($membership->get_customer()) : true;
        $is_current_user_invited = $this->isCurrentUserInvitedToChatdtcPilot();

        foreach (DTC_PRICING_TABLE_RULES as $rule) {
            if (
                $rule['is_chatdtc_table']
                && DTC_IS_CHATDTC_PILOT_ACTIVE
                && !$is_current_user_invited
            ) {
                continue;
            }
            if (
                !$rule['is_chatdtc_table']
                && !DTC_IS_CHATDTC_PILOT_ACTIVE
            ) {
                continue;
            }
            if (
                !$rule['is_chatdtc_table']
                && $is_current_user_invited
            ) {
                continue;
            }
            if (
                $rule['valid_for_users_that_paid_any_of_these_amounts_only']
                && (
                    empty($membership)
                    || $is_legacy_lost
                    || !in_array($membership->get_recurring_amount(), $rule['valid_for_users_that_paid_any_of_these_amounts_only'])
                )
            ) {
                continue;
            }
            $pricing_table_id = $rule['pricing_table_id'];
            break;
        }

        Debug::info('DTC Pricing Table: Displaying table #' . $pricing_table_id);
        return do_shortcode('[easy-pricing-table id="' . $pricing_table_id . '"]');
    }

    //
}
