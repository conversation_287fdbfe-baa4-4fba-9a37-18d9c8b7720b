[25-Jun-2025 15:25:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:12 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:12 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-INFO] DTC Membership Status Transition: Membership #34590 transitioned from new to pending
[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-DEBUG] Membership level in getRotoGptSubscription: 11
[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-DEBUG] DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE: Array
(
    [0] => Array
        (
            [membership_level_id] => 8
            [rotogpt_subscription_type] => free
        )

    [1] => Array
        (
            [membership_level_id] => 7
            [rotogpt_subscription_type] => standard_50
        )

    [2] => Array
        (
            [membership_level_id] => 9
            [rotogpt_subscription_type] => standard_100
        )

    [3] => Array
        (
            [membership_level_id] => 12
            [rotogpt_subscription_type] => standard_200
        )

    [4] => Array
        (
            [membership_level_id] => 5
            [rotogpt_subscription_type] => admin
        )

)

[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-INFO] DTC RotoGPT: New membership added - membership ID: 34590
[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-INFO] DTC RotoGPT Processing: Processing membership #34590 - membership_level: 11, customer_id: 25813, membership_status: pending, is_upgrade: 1
[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-INFO] DTC RotoGPT: Membership #34590 is an upgrade/downgrade - using update endpoint
[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-DEBUG] Membership level in getRotoGptSubscription: 12
[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-DEBUG] DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE: Array
(
    [0] => Array
        (
            [membership_level_id] => 8
            [rotogpt_subscription_type] => free
        )

    [1] => Array
        (
            [membership_level_id] => 7
            [rotogpt_subscription_type] => standard_50
        )

    [2] => Array
        (
            [membership_level_id] => 9
            [rotogpt_subscription_type] => standard_100
        )

    [3] => Array
        (
            [membership_level_id] => 12
            [rotogpt_subscription_type] => standard_200
        )

    [4] => Array
        (
            [membership_level_id] => 5
            [rotogpt_subscription_type] => admin
        )

)

[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-DEBUG] Found mapping for membership level 12: Array
(
    [membership_level_id] => 12
    [rotogpt_subscription_type] => standard_200
)

[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-DEBUG] Membership level in getRotoGptSubscription: 11
[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-DEBUG] DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE: Array
(
    [0] => Array
        (
            [membership_level_id] => 8
            [rotogpt_subscription_type] => free
        )

    [1] => Array
        (
            [membership_level_id] => 7
            [rotogpt_subscription_type] => standard_50
        )

    [2] => Array
        (
            [membership_level_id] => 9
            [rotogpt_subscription_type] => standard_100
        )

    [3] => Array
        (
            [membership_level_id] => 12
            [rotogpt_subscription_type] => standard_200
        )

    [4] => Array
        (
            [membership_level_id] => 5
            [rotogpt_subscription_type] => admin
        )

)

[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-INFO] DTC Downgrade Fix: Detected downgrade - preserving current membership until expiration
[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-INFO] DTC Downgrade Fix: Preserving expiration date: July 25, 2025
[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-INFO] DTC Downgrade Fix: Successfully scheduled downgrade for July 25, 2025
[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-INFO] DTC RotoGPT Signin: Sending request to https://api.dev.rotogpt.com/signin
[25-Jun-2025 15:25:12 UTC] [2025-06-25 15:25:12] [DTC-DEBUG] DTC RotoGPT Signin: Request body: Array
(
    [client_id] => DTC
    [client_password] => 7aMBpDiOxWKBL9H1-wF9LPSIKI_SoNOtH7vnz_naLno=
    [current_user] => Array
        (
            [user_id] => 25813
            [membership] => free
            [sign_up_date] => 25-07-2025 00:00:00
        )

)

[25-Jun-2025 15:25:13 UTC] [2025-06-25 15:25:13] [DTC-DEBUG] DTC RotoGPT Signin: Response: Array
(
    [headers] => WpOrg\Requests\Utility\CaseInsensitiveDictionary Object
        (
            [data:protected] => Array
                (
                    [date] => Wed, 25 Jun 2025 15:25:12 GMT
                    [server] => uvicorn
                    [content-length] => 551
                    [content-type] => application/json
                    [via] => 1.1 google
                    [alt-svc] => h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
                )

        )

    [body] => {"accessToken":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODEzIiwibWVtYmVyc2hpcCI6ImZyZWUiLCJzaWduX3VwX2RhdGUiOiIyNS0wNy0yMDI1IDAwOjAwOjAwIiwidXNlckRhdGEiOnsiY3JlZGl0cyI6MjAwLCJhbGxvd2VkX21vZGVscyI6WyJSb3RvR1BUIDQuMCIsIlJvdG9HUFQgMy41IiwiUm90b0dQVCAzLjUuMSIsIlJvdG9HUFQgNC41IiwiUm90b0dQVCA1LjAiXSwidGVybXNBbmRDb25kaXRpb25zIjpudWxsfSwidXNlclN0YXR1cyI6Im5vdF9zdXJ2ZXkiLCJleHAiOjE3NTIwNzQ3MTJ9.wMS1QdHQszqYmWa1tTjtJkH-lGgPQML6G1h--pqAJ7o"}
    [response] => Array
        (
            [code] => 200
            [message] => OK
        )

    [cookies] => Array
        (
        )

    [filename] => 
    [http_response] => WP_HTTP_Requests_Response Object
        (
            [response:protected] => WpOrg\Requests\Response Object
                (
                    [body] => {"accessToken":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODEzIiwibWVtYmVyc2hpcCI6ImZyZWUiLCJzaWduX3VwX2RhdGUiOiIyNS0wNy0yMDI1IDAwOjAwOjAwIiwidXNlckRhdGEiOnsiY3JlZGl0cyI6MjAwLCJhbGxvd2VkX21vZGVscyI6WyJSb3RvR1BUIDQuMCIsIlJvdG9HUFQgMy41IiwiUm90b0dQVCAzLjUuMSIsIlJvdG9HUFQgNC41IiwiUm90b0dQVCA1LjAiXSwidGVybXNBbmRDb25kaXRpb25zIjpudWxsfSwidXNlclN0YXR1cyI6Im5vdF9zdXJ2ZXkiLCJleHAiOjE3NTIwNzQ3MTJ9.wMS1QdHQszqYmWa1tTjtJkH-lGgPQML6G1h--pqAJ7o"}
                    [raw] => HTTP/1.1 200 OK
date: Wed, 25 Jun 2025 15:25:12 GMT
server: uvicorn
Content-Length: 551
content-type: application/json
Via: 1.1 google
Alt-Svc: h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
Connection: close

{"accessToken":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODEzIiwibWVtYmVyc2hpcCI6ImZyZWUiLCJzaWduX3VwX2RhdGUiOiIyNS0wNy0yMDI1IDAwOjAwOjAwIiwidXNlckRhdGEiOnsiY3JlZGl0cyI6MjAwLCJhbGxvd2VkX21vZGVscyI6WyJSb3RvR1BUIDQuMCIsIlJvdG9HUFQgMy41IiwiUm90b0dQVCAzLjUuMSIsIlJvdG9HUFQgNC41IiwiUm90b0dQVCA1LjAiXSwidGVybXNBbmRDb25kaXRpb25zIjpudWxsfSwidXNlclN0YXR1cyI6Im5vdF9zdXJ2ZXkiLCJleHAiOjE3NTIwNzQ3MTJ9.wMS1QdHQszqYmWa1tTjtJkH-lGgPQML6G1h--pqAJ7o"}
                    [headers] => WpOrg\Requests\Response\Headers Object
                        (
                            [data:protected] => Array
                                (
                                    [date] => Array
                                        (
                                            [0] => Wed, 25 Jun 2025 15:25:12 GMT
                                        )

                                    [server] => Array
                                        (
                                            [0] => uvicorn
                                        )

                                    [content-length] => Array
                                        (
                                            [0] => 551
                                        )

                                    [content-type] => Array
                                        (
                                            [0] => application/json
                                        )

                                    [via] => Array
                                        (
                                            [0] => 1.1 google
                                        )

                                    [alt-svc] => Array
                                        (
                                            [0] => h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
                                        )

                                )

                        )

                    [status_code] => 200
                    [protocol_version] => 1.1
                    [success] => 1
                    [redirects] => 0
                    [url] => https://api.dev.rotogpt.com/signin
                    [history] => Array
                        (
                        )

                    [cookies] => WpOrg\Requests\Cookie\Jar Object
                        (
                            [cookies:protected] => Array
                                (
                                )

                        )

                )

            [filename:protected] => 
            [data] => 
            [headers] => 
            [status] => 
        )

)

[25-Jun-2025 15:25:13 UTC] [2025-06-25 15:25:13] [DTC-DEBUG] DTC RotoGPT Signin: Response body: Array
(
    [accessToken] => eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODEzIiwibWVtYmVyc2hpcCI6ImZyZWUiLCJzaWduX3VwX2RhdGUiOiIyNS0wNy0yMDI1IDAwOjAwOjAwIiwidXNlckRhdGEiOnsiY3JlZGl0cyI6MjAwLCJhbGxvd2VkX21vZGVscyI6WyJSb3RvR1BUIDQuMCIsIlJvdG9HUFQgMy41IiwiUm90b0dQVCAzLjUuMSIsIlJvdG9HUFQgNC41IiwiUm90b0dQVCA1LjAiXSwidGVybXNBbmRDb25kaXRpb25zIjpudWxsfSwidXNlclN0YXR1cyI6Im5vdF9zdXJ2ZXkiLCJleHAiOjE3NTIwNzQ3MTJ9.wMS1QdHQszqYmWa1tTjtJkH-lGgPQML6G1h--pqAJ7o
)

[25-Jun-2025 15:25:13 UTC] [2025-06-25 15:25:13] [DTC-INFO] DTC RotoGPT Update: Sending request to https://api.dev.rotogpt.com/subscriptions/update
[25-Jun-2025 15:25:13 UTC] [2025-06-25 15:25:13] [DTC-DEBUG] DTC RotoGPT Update: Request body: Array
(
    [user_id] => 25813
    [client_id] => DTC
    [subscription_type] => free
    [apply_immediately] => 
    [new_subscription_start_date] => 2025-07-25T00:00:00Z
)

[25-Jun-2025 15:25:13 UTC] [2025-06-25 15:25:13] [DTC-INFO] DTC RotoGPT Update: Response code: 200
[25-Jun-2025 15:25:13 UTC] [2025-06-25 15:25:13] [DTC-DEBUG] DTC RotoGPT Update: Response body: {"status":"success","message":"Subscription change for user 25813 to free has been scheduled for 2025-07-25.","user_id":"25813"}
[25-Jun-2025 15:25:13 UTC] [2025-06-25 15:25:13] [DTC-INFO] DTC RotoGPT Downgrade Success: Scheduled user #25813 from standard_200 to free starting 2025-07-25T00:00:00Z
[25-Jun-2025 15:25:13 UTC] PHP Deprecated:  Function RCP_Registration::get_subscription is <strong>deprecated</strong> since version 3.0! Use RCP_Registration:get_membership_level_id instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:13 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:13 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:13 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:13 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:13 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:13 UTC] PHP Warning:  file_get_contents(): SSL operation failed with code 1. OpenSSL Error messages:
error:0A000126:SSL routines::unexpected eof while reading in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/dynasty-trade-calculator/user/rcp.php on line 58
[25-Jun-2025 15:25:13 UTC] PHP Warning:  file_get_contents(): SSL: Undefined error: 0 in /Users/<USER>/Sites/dynastytradecalculator/wp-content/plugins/dynasty-trade-calculator/user/rcp.php on line 58
[25-Jun-2025 15:25:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:16 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:17 UTC] [2025-06-25 15:25:17] [DTC-DEBUG] Membership level in getRotoGptSubscription: 12
[25-Jun-2025 15:25:17 UTC] [2025-06-25 15:25:17] [DTC-DEBUG] DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE: Array
(
    [0] => Array
        (
            [membership_level_id] => 8
            [rotogpt_subscription_type] => free
        )

    [1] => Array
        (
            [membership_level_id] => 7
            [rotogpt_subscription_type] => standard_50
        )

    [2] => Array
        (
            [membership_level_id] => 9
            [rotogpt_subscription_type] => standard_100
        )

    [3] => Array
        (
            [membership_level_id] => 12
            [rotogpt_subscription_type] => standard_200
        )

    [4] => Array
        (
            [membership_level_id] => 5
            [rotogpt_subscription_type] => admin
        )

)

[25-Jun-2025 15:25:17 UTC] [2025-06-25 15:25:17] [DTC-DEBUG] Found mapping for membership level 12: Array
(
    [membership_level_id] => 12
    [rotogpt_subscription_type] => standard_200
)

[25-Jun-2025 15:25:17 UTC] [2025-06-25 15:25:17] [DTC-DEBUG] Membership level in getRotoGptSubscription: 11
[25-Jun-2025 15:25:17 UTC] [2025-06-25 15:25:17] [DTC-DEBUG] DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE: Array
(
    [0] => Array
        (
            [membership_level_id] => 8
            [rotogpt_subscription_type] => free
        )

    [1] => Array
        (
            [membership_level_id] => 7
            [rotogpt_subscription_type] => standard_50
        )

    [2] => Array
        (
            [membership_level_id] => 9
            [rotogpt_subscription_type] => standard_100
        )

    [3] => Array
        (
            [membership_level_id] => 12
            [rotogpt_subscription_type] => standard_200
        )

    [4] => Array
        (
            [membership_level_id] => 5
            [rotogpt_subscription_type] => admin
        )

)

[25-Jun-2025 15:25:17 UTC] [2025-06-25 15:25:17] [DTC-INFO] DTC Downgrade Fix: Preventing automatic activation of scheduled downgrade membership #34590
[25-Jun-2025 15:25:17 UTC] [2025-06-25 15:25:17] [DTC-INFO] DTC Downgrade Fix: Completed downgrade registration without activation for payment #134174
[25-Jun-2025 15:25:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:22 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[25-Jun-2025 15:25:23 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[25-Jun-2025 15:25:23 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:23 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:23 UTC] [2025-06-25 15:25:23] [DTC-DEBUG] Membership level in getRotoGptSubscription: 12
[25-Jun-2025 15:25:23 UTC] [2025-06-25 15:25:23] [DTC-DEBUG] DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE: Array
(
    [0] => Array
        (
            [membership_level_id] => 8
            [rotogpt_subscription_type] => free
        )

    [1] => Array
        (
            [membership_level_id] => 7
            [rotogpt_subscription_type] => standard_50
        )

    [2] => Array
        (
            [membership_level_id] => 9
            [rotogpt_subscription_type] => standard_100
        )

    [3] => Array
        (
            [membership_level_id] => 12
            [rotogpt_subscription_type] => standard_200
        )

    [4] => Array
        (
            [membership_level_id] => 5
            [rotogpt_subscription_type] => admin
        )

)

[25-Jun-2025 15:25:23 UTC] [2025-06-25 15:25:23] [DTC-DEBUG] Found mapping for membership level 12: Array
(
    [membership_level_id] => 12
    [rotogpt_subscription_type] => standard_200
)

[25-Jun-2025 15:25:23 UTC] [2025-06-25 15:25:23] [DTC-INFO] DTC RotoGPT Signin: Sending request to https://api.dev.rotogpt.com/signin
[25-Jun-2025 15:25:23 UTC] [2025-06-25 15:25:23] [DTC-DEBUG] DTC RotoGPT Signin: Request body: Array
(
    [client_id] => DTC
    [client_password] => 7aMBpDiOxWKBL9H1-wF9LPSIKI_SoNOtH7vnz_naLno=
    [current_user] => Array
        (
            [user_id] => 25813
            [membership] => standard_200
            [sign_up_date] => 25-06-2025 10:24:24
        )

)

[25-Jun-2025 15:25:24 UTC] [2025-06-25 15:25:24] [DTC-DEBUG] DTC RotoGPT Signin: Response: Array
(
    [headers] => WpOrg\Requests\Utility\CaseInsensitiveDictionary Object
        (
            [data:protected] => Array
                (
                    [date] => Wed, 25 Jun 2025 15:25:23 GMT
                    [server] => uvicorn
                    [content-length] => 562
                    [content-type] => application/json
                    [via] => 1.1 google
                    [alt-svc] => h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
                )

        )

    [body] => {"accessToken":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODEzIiwibWVtYmVyc2hpcCI6InN0YW5kYXJkXzIwMCIsInNpZ25fdXBfZGF0ZSI6IjI1LTA2LTIwMjUgMTA6MjQ6MjQiLCJ1c2VyRGF0YSI6eyJjcmVkaXRzIjoyMDAsImFsbG93ZWRfbW9kZWxzIjpbIlJvdG9HUFQgNC4wIiwiUm90b0dQVCAzLjUiLCJSb3RvR1BUIDMuNS4xIiwiUm90b0dQVCA0LjUiLCJSb3RvR1BUIDUuMCJdLCJ0ZXJtc0FuZENvbmRpdGlvbnMiOm51bGx9LCJ1c2VyU3RhdHVzIjoibm90X3N1cnZleSIsImV4cCI6MTc1MjA3NDcyNH0.dG40gEd4OWRU5viFWM-mCaH0G0RpkLEqToWvNSJZHN0"}
    [response] => Array
        (
            [code] => 200
            [message] => OK
        )

    [cookies] => Array
        (
        )

    [filename] => 
    [http_response] => WP_HTTP_Requests_Response Object
        (
            [response:protected] => WpOrg\Requests\Response Object
                (
                    [body] => {"accessToken":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODEzIiwibWVtYmVyc2hpcCI6InN0YW5kYXJkXzIwMCIsInNpZ25fdXBfZGF0ZSI6IjI1LTA2LTIwMjUgMTA6MjQ6MjQiLCJ1c2VyRGF0YSI6eyJjcmVkaXRzIjoyMDAsImFsbG93ZWRfbW9kZWxzIjpbIlJvdG9HUFQgNC4wIiwiUm90b0dQVCAzLjUiLCJSb3RvR1BUIDMuNS4xIiwiUm90b0dQVCA0LjUiLCJSb3RvR1BUIDUuMCJdLCJ0ZXJtc0FuZENvbmRpdGlvbnMiOm51bGx9LCJ1c2VyU3RhdHVzIjoibm90X3N1cnZleSIsImV4cCI6MTc1MjA3NDcyNH0.dG40gEd4OWRU5viFWM-mCaH0G0RpkLEqToWvNSJZHN0"}
                    [raw] => HTTP/1.1 200 OK
date: Wed, 25 Jun 2025 15:25:23 GMT
server: uvicorn
Content-Length: 562
content-type: application/json
Via: 1.1 google
Alt-Svc: h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
Connection: close

{"accessToken":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODEzIiwibWVtYmVyc2hpcCI6InN0YW5kYXJkXzIwMCIsInNpZ25fdXBfZGF0ZSI6IjI1LTA2LTIwMjUgMTA6MjQ6MjQiLCJ1c2VyRGF0YSI6eyJjcmVkaXRzIjoyMDAsImFsbG93ZWRfbW9kZWxzIjpbIlJvdG9HUFQgNC4wIiwiUm90b0dQVCAzLjUiLCJSb3RvR1BUIDMuNS4xIiwiUm90b0dQVCA0LjUiLCJSb3RvR1BUIDUuMCJdLCJ0ZXJtc0FuZENvbmRpdGlvbnMiOm51bGx9LCJ1c2VyU3RhdHVzIjoibm90X3N1cnZleSIsImV4cCI6MTc1MjA3NDcyNH0.dG40gEd4OWRU5viFWM-mCaH0G0RpkLEqToWvNSJZHN0"}
                    [headers] => WpOrg\Requests\Response\Headers Object
                        (
                            [data:protected] => Array
                                (
                                    [date] => Array
                                        (
                                            [0] => Wed, 25 Jun 2025 15:25:23 GMT
                                        )

                                    [server] => Array
                                        (
                                            [0] => uvicorn
                                        )

                                    [content-length] => Array
                                        (
                                            [0] => 562
                                        )

                                    [content-type] => Array
                                        (
                                            [0] => application/json
                                        )

                                    [via] => Array
                                        (
                                            [0] => 1.1 google
                                        )

                                    [alt-svc] => Array
                                        (
                                            [0] => h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
                                        )

                                )

                        )

                    [status_code] => 200
                    [protocol_version] => 1.1
                    [success] => 1
                    [redirects] => 0
                    [url] => https://api.dev.rotogpt.com/signin
                    [history] => Array
                        (
                        )

                    [cookies] => WpOrg\Requests\Cookie\Jar Object
                        (
                            [cookies:protected] => Array
                                (
                                )

                        )

                )

            [filename:protected] => 
            [data] => 
            [headers] => 
            [status] => 
        )

)

[25-Jun-2025 15:25:24 UTC] [2025-06-25 15:25:24] [DTC-DEBUG] DTC RotoGPT Signin: Response body: Array
(
    [accessToken] => eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnQiOnsiY2xpZW50X2lkIjoiRFRDIiwiaWRfdG9fdXNlIjoiZHRjSWQiLCJjbGllbnRfbmFtZV90b191c2UiOiJEVEMifSwidXNlcl9pZCI6IjI1ODEzIiwibWVtYmVyc2hpcCI6InN0YW5kYXJkXzIwMCIsInNpZ25fdXBfZGF0ZSI6IjI1LTA2LTIwMjUgMTA6MjQ6MjQiLCJ1c2VyRGF0YSI6eyJjcmVkaXRzIjoyMDAsImFsbG93ZWRfbW9kZWxzIjpbIlJvdG9HUFQgNC4wIiwiUm90b0dQVCAzLjUiLCJSb3RvR1BUIDMuNS4xIiwiUm90b0dQVCA0LjUiLCJSb3RvR1BUIDUuMCJdLCJ0ZXJtc0FuZENvbmRpdGlvbnMiOm51bGx9LCJ1c2VyU3RhdHVzIjoibm90X3N1cnZleSIsImV4cCI6MTc1MjA3NDcyNH0.dG40gEd4OWRU5viFWM-mCaH0G0RpkLEqToWvNSJZHN0
)

[25-Jun-2025 15:25:24 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:24 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:30 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:30 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:30 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:30 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:30 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:30 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[25-Jun-2025 15:25:31 UTC] PHP Deprecated:  uksort(): Returning bool from comparison function is deprecated, return an integer less than, equal to, or greater than zero in /Users/<USER>/Sites/dynastytradecalculator/wp-content/themes/Avada/includes/lib/inc/class-fusion-dynamic-css.php on line 685
[25-Jun-2025 15:25:31 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:31 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:31 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:25:31 UTC] PHP Deprecated:  Function get_currentuserinfo is <strong>deprecated</strong> since version 4.5.0! Use wp_get_current_user() instead. in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:26:00 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:26:00 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:26:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:26:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:26:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:26:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:26:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:26:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:28:00 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>advanced-cron-manager</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:28:00 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>jonradio-remember-me</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:28:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:28:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affiliate-wp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:28:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>affwp-paypal-payouts</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:28:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:28:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>Avada</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
[25-Jun-2025 15:28:01 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>fusion-builder</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/dynastytradecalculator/wp-includes/functions.php on line 6121
